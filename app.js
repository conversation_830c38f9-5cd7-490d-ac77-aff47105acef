require("dotenv").config();

const express = require("express");
const bodyParser = require("body-parser");
const methodOverride = require("method-override");
const compression = require("compression");
const cors = require("cors");

// Initialize Express app
const app = express();
const apiVersion = "/api/v1";

// Set port
const PORT = process.env.PORT || 8000;
app.set("port", PORT);

// Middleware
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());
app.use(methodOverride("_method"));
app.use(express.static("public")); // Simplified static directory
app.use(compression());
app.use(cors());

// Import routers
const routes = [
    require("./src/routes/users"),
    require("./src/routes/auth"),
    require("./src/routes/reservation"),
    require("./src/routes/rates"),
    require("./src/routes/email"),
    require("./src/routes/rates_control"),
    require("./src/routes/service_extra"),
];

// Use routes
routes.forEach(route => app.use(apiVersion, route));

// Start server
app.listen(PORT, () => {
    console.log(`Running API server on port ${PORT}`);
});