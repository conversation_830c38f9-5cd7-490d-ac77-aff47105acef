const { pool } = require("../database");

// Crear tabla de logs si no existe
const createLogsTable = () => {
    const sql = `
        CREATE TABLE IF NOT EXISTS ERROR_LOGS (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            session_id VARCHAR(255),
            user_id VARCHAR(255),
            component VARCHAR(255) NOT NULL,
            action VARCHAR(255) NOT NULL,
            endpoint VARCHAR(255),
            params JSON,
            error_message TEXT,
            error_status INT,
            error_code VARCHAR(50),
            error_data JSON,
            browser_info JSON,
            environment VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_timestamp (timestamp),
            INDEX idx_component (component),
            INDEX idx_session (session_id)
        )
    `;
    
    pool.query(sql, (error) => {
        if (error) {
            console.error("Error creating ERROR_LOGS table:", error);
        } else {
            console.log("ERROR_LOGS table ready");
        }
    });
};

// Inicializar tabla al cargar el módulo
createLogsTable();

// Guardar log de error
exports.saveErrorLog = (req, res) => {
    try {
        const logData = req.body;
        
        // Validar datos requeridos
        if (!logData.component || !logData.action) {
            return res.status(400).json({ 
                status: 400, 
                message: "Component and action are required" 
            });
        }

        const sql = `
            INSERT INTO ERROR_LOGS (
                timestamp, session_id, user_id, component, action, 
                endpoint, params, error_message, error_status, 
                error_code, error_data, browser_info, environment
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const values = [
            new Date(logData.timestamp),
            logData.sessionId,
            logData.userId,
            logData.component,
            logData.action,
            logData.endpoint,
            JSON.stringify(logData.params || {}),
            logData.error?.message,
            logData.error?.status,
            logData.error?.code,
            JSON.stringify(logData.error?.data || {}),
            JSON.stringify(logData.browser || {}),
            logData.environment
        ];

        pool.query(sql, values, (error, result) => {
            if (error) {
                console.error("Error saving error log:", error);
                return res.status(500).json({ 
                    status: 500, 
                    message: "Failed to save error log" 
                });
            }

            res.status(200).json({ 
                status: 200, 
                message: "Error log saved successfully",
                logId: result.insertId
            });
        });

    } catch (error) {
        console.error("Error in saveErrorLog:", error);
        res.status(500).json({ 
            status: 500, 
            message: "Internal server error" 
        });
    }
};

// Obtener logs con filtros
exports.getErrorLogs = (req, res) => {
    try {
        const { 
            component, 
            dateFrom, 
            dateTo, 
            status, 
            limit = 100, 
            offset = 0 
        } = req.query;

        let sql = `
            SELECT 
                id, timestamp, session_id, user_id, component, action,
                endpoint, params, error_message, error_status, error_code,
                error_data, browser_info, environment, created_at
            FROM ERROR_LOGS 
            WHERE 1=1
        `;
        
        const values = [];

        if (component) {
            sql += ` AND component LIKE ?`;
            values.push(`%${component}%`);
        }

        if (dateFrom) {
            sql += ` AND timestamp >= ?`;
            values.push(dateFrom);
        }

        if (dateTo) {
            sql += ` AND timestamp <= ?`;
            values.push(dateTo + ' 23:59:59');
        }

        if (status) {
            sql += ` AND error_status = ?`;
            values.push(status);
        }

        sql += ` ORDER BY timestamp DESC LIMIT ? OFFSET ?`;
        values.push(parseInt(limit), parseInt(offset));

        pool.query(sql, values, (error, results) => {
            if (error) {
                console.error("Error fetching error logs:", error);
                return res.status(500).json({ 
                    status: 500, 
                    message: "Failed to fetch error logs" 
                });
            }

            // Parsear JSON fields
            const parsedResults = results.map(log => ({
                ...log,
                params: log.params ? JSON.parse(log.params) : {},
                error_data: log.error_data ? JSON.parse(log.error_data) : {},
                browser_info: log.browser_info ? JSON.parse(log.browser_info) : {}
            }));

            res.status(200).json({ 
                status: 200, 
                results: parsedResults,
                total: parsedResults.length
            });
        });

    } catch (error) {
        console.error("Error in getErrorLogs:", error);
        res.status(500).json({ 
            status: 500, 
            message: "Internal server error" 
        });
    }
};

// Obtener estadísticas de errores
exports.getErrorStats = (req, res) => {
    try {
        const { dateFrom, dateTo } = req.query;
        
        let sql = `
            SELECT 
                component,
                COUNT(*) as error_count,
                COUNT(DISTINCT session_id) as affected_sessions,
                error_status,
                DATE(timestamp) as error_date
            FROM ERROR_LOGS 
            WHERE 1=1
        `;
        
        const values = [];

        if (dateFrom) {
            sql += ` AND timestamp >= ?`;
            values.push(dateFrom);
        }

        if (dateTo) {
            sql += ` AND timestamp <= ?`;
            values.push(dateTo + ' 23:59:59');
        }

        sql += ` GROUP BY component, error_status, DATE(timestamp) ORDER BY error_count DESC`;

        pool.query(sql, values, (error, results) => {
            if (error) {
                console.error("Error fetching error stats:", error);
                return res.status(500).json({ 
                    status: 500, 
                    message: "Failed to fetch error stats" 
                });
            }

            res.status(200).json({ 
                status: 200, 
                results: results
            });
        });

    } catch (error) {
        console.error("Error in getErrorStats:", error);
        res.status(500).json({ 
            status: 500, 
            message: "Internal server error" 
        });
    }
};

// Limpiar logs antiguos (ejecutar como tarea programada)
exports.cleanOldLogs = (req, res) => {
    try {
        const daysToKeep = req.query.days || 30;
        
        const sql = `
            DELETE FROM ERROR_LOGS 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        `;

        pool.query(sql, [daysToKeep], (error, result) => {
            if (error) {
                console.error("Error cleaning old logs:", error);
                return res.status(500).json({ 
                    status: 500, 
                    message: "Failed to clean old logs" 
                });
            }

            res.status(200).json({ 
                status: 200, 
                message: `Cleaned ${result.affectedRows} old log entries`,
                deletedRows: result.affectedRows
            });
        });

    } catch (error) {
        console.error("Error in cleanOldLogs:", error);
        res.status(500).json({ 
            status: 500, 
            message: "Internal server error" 
        });
    }
};
