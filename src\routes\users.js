//express
const express = require("express");

//Controller-bd-action
const ctrl = require("../controllers/users_controller");
const Users = express.Router();

/** Users Routes */
Users.route("/users").get(ctrl.get);
Users.route("/users").post(ctrl.insert);
Users.route("/users/:id").get(ctrl.getById);
Users.route("/users/:id").put(ctrl.update);
Users.route("/users/:id").delete(ctrl.delete);

module.exports = Users;
