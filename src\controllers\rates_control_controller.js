const { pool } = require("../database");
const utils = require("../utils");

const _TABLE = "CONTROL_TARIFAS";
const _ID = "ID_CONTROL_TARIFAS";

exports.get = (req, res) => {
    const sql = `SELECT * FROM ${_TABLE} WHERE ACTIVO <> 3`;

    pool.query(sql, (error, result) => {
        if (error) {
            console.error("Error en la consulta:", error);
            return res.status(500).json({ status: 500, message: "Error en el servidor." });
        }

        console.log("sql rcontrol all:", result);

        const response = {
            status: 200,
            results: utils.userParser(result)
        };
        res.status(200).json(response);
    });
};

exports.getById = (req, res) => {
    const sql = `SELECT * FROM ${_TABLE} WHERE ACTIVO <> 3 AND ID_RESERVATION = ?`;
    const values = [req.params.id];

    console.log("sql reservations:", sql);

    pool.query(sql, values, (error, result) => {
        if (error) {
            console.error("Error en la consulta:", error);
            return res.status(500).json({ status: 500, message: "Error en el servidor." });
        }

        console.log("sql rcontrol by id:", result);

        const response = {
            status: 200,
            results: utils.userParser(result)
        };
        res.status(200).json(response);
    });
};

exports.insert = (req, res) => {
    try {
        utils.functionValidate(req.body, (isValid) => {
            if (isValid) {
                const sql = `INSERT INTO ${_TABLE} SET ?`;
                
                pool.query(sql, req.body, (err, result) => {
                    if (err) {
                        console.error("Error al insertar:", err);
                        return res.status(500).json({ status: 500, message: "Error al insertar el registro." });
                    }

                    const response = {
                        status: 200,
                        results: result,
                    };
                    res.status(200).json(response);
                });
            } else {
                return res.status(405).json({ status: 405, message: "Missing Parameters" });
            }
        });
    } catch (error) {
        console.error("Error en el proceso de inserción:", error);
        return res.status(500).json({ status: 500, message: "Error en el servidor." });
    }
};

exports.update = (req, res) => {
    try {
        utils.functionValidate(req.body, (isValid) => {
            if (isValid) {
                let sql = `UPDATE ${_TABLE} SET `;
                const data = [];
                let setClauses = [];

                Object.keys(req.body).forEach((key, index) => {
                    setClauses.push(`${key} = ?`);
                    data.push(req.body[key]);
                });

                sql += setClauses.join(", ") + ` WHERE ${_ID} = ?`;
                data.push(req.params.id);

                pool.query(sql, data, (err, result) => {
                    if (err) {
                        console.error("Error al actualizar:", err);
                        return res.status(500).json({ status: 500, message: "Error al actualizar el registro." });
                    }

                    const response = {
                        status: 200,
                        results: result,
                    };
                    res.status(200).json(response);
                });
            } else {
                return res.status(405).json({ status: 405, message: "Missing Parameters" });
            }
        });
    } catch (error) {
        console.error("Error en el proceso de actualización:", error);
        return res.status(500).json({ status: 500, message: "Error en el servidor." });
    }
};

exports.delete = (req, res) => {
    try {
        const sql = `UPDATE ${_TABLE} SET ACTIVO = ? WHERE ${_ID} = ?`;
        const data = [3, req.params.id];

        pool.query(sql, data, (err, result) => {
            if (err) {
                console.error("Error al eliminar:", err);
                return res.status(500).json({ status: 500, message: "Error al eliminar el registro." });
            }

            const response = {
                status: 200,
                results: result,
            };
            res.status(200).json(response);
        });
    } catch (error) {
        console.error("Error en el proceso de eliminación:", error);
        return res.status(500).json({ status: 500, message: "Error en el servidor." });
    }
};