
@import '../../../scss/variables';

.container {
    padding: 1rem;
    margin: 1rem 0;
    // border:1px solid #fb8686;
    border-radius: 8px;
    background-color: #fff;
    hr {
        margin: 0;
        border-top: 1px solid #fdc2c2;
        &:last-child {
            display: none;
        }
    }
}

.service_container {
    padding: 1rem;
    display: grid;
    grid-template-columns:10% 45% 20% 20%;
    align-items: center;
    p {
        margin-bottom: 0;
    }
}
.icon_container {
    background-color: lighten($accent-color-2, 10)  ;
    width: 30px;
    height: 30px;
    border-radius: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    svg {
        color:white;
        margin-top: 0 !important;
        margin-left: 0 !important;
    }
}
