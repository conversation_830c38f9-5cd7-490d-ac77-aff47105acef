# API-Pawnshop
Api rest para california cash

Al descargar `npm install` y crear el archivo `.env` con la siguiente info:

```
DB_HOST=host_local
DB_USER=usuario_local
DB_PASS=pass_local
DB_NAME=pawnshop

PORT=5000
```

Al instalar todo los paquetes, correr el siguiente comando `npm run dev` para que corra `nodemon` y asi poder estar agregando los cambios en tiempo real.
 
 # Rates

 En la rama Master se coloca la tarifa con descuento, 
 En la Rama Dev se coloca la tarifa neta, o publica