import axios from "axios";
import configs from "../configs/config";

// Enviar log de error al servidor
export const sendErrorLog = (errorData) => {
    return axios.post(`${configs.URL_API_BASE}/error-logs`, errorData);
};

// Obtener logs de errores (solo para admins)
export const getErrorLogs = (params = {}) => {
    return axios.get(`${configs.URL_API_BASE}/error-logs`, { params });
};

// Obtener estadísticas de errores
export const getErrorStats = (params = {}) => {
    return axios.get(`${configs.URL_API_BASE}/error-logs/stats`, { params });
};

// Limpiar logs antiguos
export const cleanOldLogs = (days = 30) => {
    return axios.delete(`${configs.URL_API_BASE}/error-logs/cleanup?days=${days}`);
};
