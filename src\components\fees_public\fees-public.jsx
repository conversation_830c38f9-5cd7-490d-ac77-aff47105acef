import React, { useState, useEffect, Fragment } from "react";
import Header from "./../utils/header";
import Footer from "./../utils/footer";
import { getFees } from "../../api_controller/fees.controller";
import FeesPublicTable from "./fees-public-table";
import LoadingOverlay from "react-loading-overlay";
import DatePicker from "react-datepicker";
import Swal from "sweetalert2";
import moment from "moment";

const loaderStyles = {
    overlay: (base) => ({
        ...base,
        background: "rgba(64, 64, 66, 0.9)",
    }),
};

const FeesPublic = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [fees, setFees] = useState([]);
    const [dateInit, setDateInit] = useState(null);
    const [dateEnd, setDateEnd] = useState(null);

    useEffect(() => {
        fetchFees();
    }, []);

    const fetchFees = (startDate, endDate) => {
        setIsLoading(true);
        let params = {};
        if (startDate && endDate) {
            params.init_date = moment(startDate).format("YYYY-MM-DD");
            params.end_date = moment(endDate).format("YYYY-MM-DD");
        }
        getFees(params)
            .then((resp) => {
                if (resp.data && Array.isArray(resp.data.results)) {
                    const mapped = resp.data.results.map((item) => ({
                        reservation_date: item.createdAt,
                        reservation_num: item.folio,
                        member_id: item.member_id,
                        real_cost: item.base_price !== null ? `$ ${item.base_price} USD` : "",
                        cost: item.total_payment ? `$ ${item.total_payment} USD` : "",
                        rci_profit: item.fee_rci !== null ? `$ ${item.fee_rci} USD` : "",
                        trans_profit: item.fee_tr !== null ? `$ ${item.fee_tr} USD` : "",
                        auto_payment: item.payment_id,
                    }));
                    setFees(mapped);
                } else {
                    setFees([]);
                }
                setIsLoading(false);
            })
            .catch((error) => {
                console.error("Error fetching fees:", error);
                setIsLoading(false);
                setFees([]);

                // Mostrar alert de error de conexión
                Swal.fire({
                    icon: "error",
                    title: "Connection Error / Error de Conexión",
                    text: "Unable to connect to the fees service. Please check your internet connection and try again. / No se pudo conectar al servicio de fees. Por favor, verifique su conexión a internet e intente nuevamente.",
                    confirmButtonText: "OK"
                });
            });
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if (!dateInit || !dateEnd) {
            Swal.fire({
                icon: "error",
                title: "Error al buscar...",
                text: "Por favor, revise que ambas fechas sean correctas.",
            });
            return;
        }
        fetchFees(dateInit, dateEnd);
    };

    // console.log(fees)

    return (
        <Fragment>
            <div id="booking-form" className="booking-form">
                <Header />
                <div className="full" id="breadcrumbs">
                    <div className="grid-container">
                        <div className="contenedor-flex grid-x grid-padding-x"></div>
                    </div>
                </div>
                <div className="grid-container">
                    <div className="grid-x grid-padding-x grid-padding-y"></div>
                    <LoadingOverlay
                        active={isLoading}
                        spinner
                        styles={loaderStyles}
                        text="We are processing your request...">
                        <form onSubmit={handleSubmit}>
                            <div className="grid-x grid-padding-x grid-padding-y">
                                <div className="small-12 medium-6 cell align-self-center pl-3">
                                    <h1>REGISTERED PUBLIC FEES</h1>
                                </div>
                                 <div className="small-12 medium-6 cell align-self-center pl-3">
                                    <div className="grid-x grid-padding-x grid-padding-y">
                                        <div className="small-6 medium-5 cell d-flex justify-content-end">
                                            <label>
                                                Initial Date
                                                <DatePicker
                                                    selected={dateInit}
                                                    onChange={setDateInit}
                                                    maxDate={dateEnd}
                                                    required
                                                    dateFormat="yyyy-MM-dd"
                                                />
                                            </label>
                                        </div>
                                        <div className="small-6 medium-5 cell d-flex justify-content-end">
                                            <label>
                                                Date End
                                                <DatePicker
                                                    selected={dateEnd}
                                                    minDate={dateInit}
                                                    onChange={setDateEnd}
                                                    required
                                                    dateFormat="yyyy-MM-dd"
                                                />
                                            </label>
                                        </div>
                                        <div className="small-12 medium-2 cell align-self-bottom d-flex justify-content-end">
                                            <button
                                                type="submit"
                                                className="button btn-primary">
                                                SEARCH
                                            </button>
                                        </div>
                                    </div>
                                 </div>
                            </div>
                        </form>
                        <div className="grid-x grid-padding-x grid-padding-y b-white">
                            <div className="small-12 cell">
                                <FeesPublicTable records={fees} />
                            </div>
                        </div>
                    </LoadingOverlay>
                </div>
                <Footer />
            </div>
        </Fragment>
    );
};

export default FeesPublic;
