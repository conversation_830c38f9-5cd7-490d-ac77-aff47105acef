var { createConnection } = require("../database");

var utils = require("./../utils");
var MainRates = require("./../resources/rates.json");

const _TABLE = "RATES";
const _ID = "ID_RATE";

const response = {
    status: 200,
    results: null,
};

exports.get = (req, res) => {
    var rates = MainRates;
    var { zone_pick, zone_destination, unit } = req.query;

    var RQ = {};
    RQ.params = {
        zone_pick,
        zone_destination,
        unit,
    };

    try {
        let _response_data = rates.zone_rates.filter((zone) => {
            return zone.name == zone_pick;
        });

        if (_response_data.length > 0) {
            let _response_rates = _response_data[0].rates.filter((rate) => {
                return rate.destination == zone_destination;
            });

            let _response_rate = _response_rates[0].rates.filter((rate) => {
                return rate.unit == unit;
            });

            RQ.success = 200;
            RQ.data = _response_rate;
        } else {
            RQ.success = 500;
            RQ.data = "Rates not found";
        }
    } catch (error) {
        RQ.success = 500;
        RQ.data = "Send correct parameters";
    }
    res.status(200).json({ response: RQ });
};
