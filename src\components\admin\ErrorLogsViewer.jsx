import React, { useState, useEffect } from 'react';
import { getErrorLogs, clearErrorLogs } from '../../utils/errorLogger';
import './ErrorLogsViewer.css';

const ErrorLogsViewer = () => {
    const [logs, setLogs] = useState([]);
    const [filteredLogs, setFilteredLogs] = useState([]);
    const [filter, setFilter] = useState({
        component: '',
        dateFrom: '',
        dateTo: '',
        status: ''
    });
    const [selectedLog, setSelectedLog] = useState(null);

    useEffect(() => {
        loadLogs();
    }, []);

    useEffect(() => {
        applyFilters();
    }, [logs, filter]);

    const loadLogs = () => {
        const errorLogs = getErrorLogs();
        setLogs(errorLogs.reverse()); // Más recientes primero
    };

    const applyFilters = () => {
        let filtered = [...logs];

        if (filter.component) {
            filtered = filtered.filter(log => 
                log.component.toLowerCase().includes(filter.component.toLowerCase())
            );
        }

        if (filter.dateFrom) {
            filtered = filtered.filter(log => 
                new Date(log.timestamp) >= new Date(filter.dateFrom)
            );
        }

        if (filter.dateTo) {
            filtered = filtered.filter(log => 
                new Date(log.timestamp) <= new Date(filter.dateTo + 'T23:59:59')
            );
        }

        if (filter.status) {
            filtered = filtered.filter(log => 
                log.error.status?.toString() === filter.status
            );
        }

        setFilteredLogs(filtered);
    };

    const handleClearLogs = () => {
        if (window.confirm('¿Estás seguro de que quieres limpiar todos los logs?')) {
            clearErrorLogs();
            setLogs([]);
            setFilteredLogs([]);
        }
    };

    const downloadLogs = () => {
        const dataStr = JSON.stringify(logs, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `error-logs-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
    };

    const getStatusColor = (status) => {
        if (!status) return 'gray';
        if (status >= 500) return 'red';
        if (status >= 400) return 'orange';
        return 'blue';
    };

    const formatDate = (timestamp) => {
        return new Date(timestamp).toLocaleString('es-MX');
    };

    return (
        <div className="error-logs-viewer">
            <div className="logs-header">
                <h2>Error Logs Viewer</h2>
                <div className="logs-actions">
                    <button onClick={loadLogs} className="btn btn-primary">
                        🔄 Refresh
                    </button>
                    <button onClick={downloadLogs} className="btn btn-secondary">
                        📥 Download JSON
                    </button>
                    <button onClick={handleClearLogs} className="btn btn-danger">
                        🗑️ Clear All
                    </button>
                </div>
            </div>

            {/* Filtros */}
            <div className="logs-filters">
                <div className="filter-row">
                    <input
                        type="text"
                        placeholder="Filter by component..."
                        value={filter.component}
                        onChange={(e) => setFilter({...filter, component: e.target.value})}
                        className="filter-input"
                    />
                    <input
                        type="date"
                        value={filter.dateFrom}
                        onChange={(e) => setFilter({...filter, dateFrom: e.target.value})}
                        className="filter-input"
                    />
                    <input
                        type="date"
                        value={filter.dateTo}
                        onChange={(e) => setFilter({...filter, dateTo: e.target.value})}
                        className="filter-input"
                    />
                    <select
                        value={filter.status}
                        onChange={(e) => setFilter({...filter, status: e.target.value})}
                        className="filter-input"
                    >
                        <option value="">All Status</option>
                        <option value="400">400 - Bad Request</option>
                        <option value="401">401 - Unauthorized</option>
                        <option value="403">403 - Forbidden</option>
                        <option value="404">404 - Not Found</option>
                        <option value="500">500 - Server Error</option>
                        <option value="502">502 - Bad Gateway</option>
                        <option value="503">503 - Service Unavailable</option>
                    </select>
                </div>
            </div>

            {/* Lista de logs */}
            <div className="logs-container">
                <div className="logs-summary">
                    Total: {logs.length} | Filtered: {filteredLogs.length}
                </div>
                
                <div className="logs-list">
                    {filteredLogs.map((log, index) => (
                        <div 
                            key={index} 
                            className="log-item"
                            onClick={() => setSelectedLog(log)}
                        >
                            <div className="log-header">
                                <span className="log-timestamp">
                                    {formatDate(log.timestamp)}
                                </span>
                                <span className="log-component">
                                    {log.component} → {log.action}
                                </span>
                                <span 
                                    className={`log-status status-${getStatusColor(log.error.status)}`}
                                >
                                    {log.error.status || 'Network Error'}
                                </span>
                            </div>
                            <div className="log-message">
                                {log.error.message}
                            </div>
                            <div className="log-endpoint">
                                {log.endpoint && `Endpoint: ${log.endpoint}`}
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Modal de detalles */}
            {selectedLog && (
                <div className="log-modal-overlay" onClick={() => setSelectedLog(null)}>
                    <div className="log-modal" onClick={(e) => e.stopPropagation()}>
                        <div className="modal-header">
                            <h3>Error Details</h3>
                            <button onClick={() => setSelectedLog(null)}>✕</button>
                        </div>
                        <div className="modal-content">
                            <pre>{JSON.stringify(selectedLog, null, 2)}</pre>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ErrorLogsViewer;
