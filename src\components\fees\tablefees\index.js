import React, { useMemo } from "react";
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';
import ReactDatatable from "@ashvin27/react-datatable";
import "font-awesome/css/font-awesome.min.css";

const TableFees = ({ records }) => {
    const columns = useMemo(() => [
        {
            key: "reservation_date",
            text: "RESERVATION DATE",
            className: "reservation_date",
            align: "center",
            sortable: true,
        },
        {
            key: "reservation_num",
            text: "RESERVATION NUMBER",
            className: "reservation_num",
            align: "center",
            sortable: true,
        },
        {
            key: "real_cost",
            text: "REAL COST",
            className: "real_cost",
            align: "center",
            sortable: true,
        },
        {
            key: "cost",
            text: "TOTAL COST",
            className: "cost",
            align: "center",
            sortable: true,
        },
        {
            key: "isDiscount",
            text: "DISCOUNT",
            className: "isDiscount",
            align: "center",
            sortable: true,
        },
        {
            key: "rci_profit",
            text: "FEES RCI",
            className: "fees_rci",
            align: "center",
            sortable: true,
        },
        {
            key: "trans_profit",
            text: "FEES TRANSROUTE",
            className: "fees_transroute",
            align: "center",
            sortable: true,
        },
        {
            key: "auto_payment",
            text: "AUTH CODE",
            className: "auto_payment",
            align: "center",
            sortable: true,
        },
    ], []);

    const config = useMemo(() => ({
        page_size: 50,
        length_menu: [50, 100, 200],
        button: { excel: false },
        //sort: {column:"", order:"desc"}
    }), []);

    const exportExcel = () => {
        const ws = XLSX.utils.json_to_sheet(records);
        const wb = { Sheets: { data: ws }, SheetNames: ['data'] };
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob(
            [excelBuffer],
            { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;' }
        );
        FileSaver.saveAs(blob, 'fees.xlsx');
    };

    return (<>
        <div className="medium-4 cell d-flex justify-content-end align-items-end">
            <button type="button" className="button btn-green" onClick={exportExcel}>Export to Excell</button>
        </div>
        <ReactDatatable
            className="table stack b-white hover table-bordered"
            key={"table22"}
            config={config}
            records={records}
            columns={columns}
            />
    </>
    );
};

export default TableFees;
