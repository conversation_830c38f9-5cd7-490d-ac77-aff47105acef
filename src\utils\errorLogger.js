/**
 * <PERSON>rror Logger Utility
 * Proporciona logging detallado para errores en la aplicación
 */

class ErrorLogger {
    constructor() {
        this.isProduction = process.env.NODE_ENV === 'production';
        this.logEndpoint = null; // Aquí puedes configurar un endpoint para enviar logs
    }

    /**
     * Log de error detallado
     * @param {Object} errorInfo - Información del error
     * @param {string} errorInfo.component - Componente donde ocurrió el error
     * @param {string} errorInfo.action - Acción que se estaba ejecutando
     * @param {string} errorInfo.endpoint - Endpoint que falló (opcional)
     * @param {Object} errorInfo.params - Parámetros enviados (opcional)
     * @param {Error} error - Objeto de error
     */
    logError(errorInfo, error) {
        const errorDetails = {
            timestamp: new Date().toISOString(),
            sessionId: this.getSessionId(),
            userId: this.getUserId(),
            component: errorInfo.component,
            action: errorInfo.action,
            endpoint: errorInfo.endpoint,
            params: errorInfo.params,
            error: {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                code: error.code,
                stack: this.isProduction ? null : error.stack // No enviar stack en producción
            },
            browser: {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: Date.now()
            },
            environment: this.isProduction ? 'production' : 'development'
        };

        // Log en consola (siempre)
        console.group(`🚨 ERROR: ${errorInfo.component} - ${errorInfo.action}`);
        console.error("Error Details:", errorDetails);
        console.error("Full Error Object:", error);
        console.groupEnd();

        // Enviar a servicio externo si está configurado
        if (this.logEndpoint) {
            this.sendToLogService(errorDetails);
        }

        // Guardar en localStorage para debugging
        this.saveToLocalStorage(errorDetails);

        return errorDetails;
    }

    /**
     * Log específico para errores de API
     */
    logApiError(component, action, endpoint, params, error) {
        return this.logError({
            component,
            action,
            endpoint,
            params
        }, error);
    }

    /**
     * Obtener ID de sesión (puedes implementar tu lógica)
     */
    getSessionId() {
        let sessionId = sessionStorage.getItem('app_session_id');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('app_session_id', sessionId);
        }
        return sessionId;
    }

    /**
     * Obtener ID de usuario si está logueado
     */
    getUserId() {
        try {
            const token = localStorage.getItem('apitoken_rciplatform');
            if (token) {
                // Aquí puedes decodificar el JWT para obtener el user ID
                return 'logged_user'; // Placeholder
            }
            return 'anonymous';
        } catch (e) {
            return 'anonymous';
        }
    }

    /**
     * Enviar logs a servicio externo
     */
    async sendToLogService(errorDetails) {
        try {
            if (!this.logEndpoint) return;
            
            await fetch(this.logEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(errorDetails)
            });
        } catch (e) {
            console.warn('Failed to send error to logging service:', e);
        }
    }

    /**
     * Guardar en localStorage para debugging local
     */
    saveToLocalStorage(errorDetails) {
        try {
            const errors = JSON.parse(localStorage.getItem('app_error_logs') || '[]');
            errors.push(errorDetails);
            
            // Mantener solo los últimos 50 errores
            if (errors.length > 50) {
                errors.splice(0, errors.length - 50);
            }
            
            localStorage.setItem('app_error_logs', JSON.stringify(errors));
        } catch (e) {
            console.warn('Failed to save error to localStorage:', e);
        }
    }

    /**
     * Obtener logs guardados localmente
     */
    getLocalLogs() {
        try {
            return JSON.parse(localStorage.getItem('app_error_logs') || '[]');
        } catch (e) {
            return [];
        }
    }

    /**
     * Limpiar logs locales
     */
    clearLocalLogs() {
        localStorage.removeItem('app_error_logs');
    }

    /**
     * Configurar endpoint para envío de logs
     */
    setLogEndpoint(endpoint) {
        this.logEndpoint = endpoint;
    }
}

// Instancia singleton
const errorLogger = new ErrorLogger();

export default errorLogger;

// Funciones de conveniencia
export const logApiError = (component, action, endpoint, params, error) => {
    return errorLogger.logApiError(component, action, endpoint, params, error);
};

export const logError = (errorInfo, error) => {
    return errorLogger.logError(errorInfo, error);
};

export const getErrorLogs = () => {
    return errorLogger.getLocalLogs();
};

export const clearErrorLogs = () => {
    errorLogger.clearLocalLogs();
};

// Función global para debugging desde la consola del navegador
if (typeof window !== 'undefined') {
    window.getAppErrorLogs = () => {
        const logs = getErrorLogs();
        console.table(logs.map(log => ({
            timestamp: log.timestamp,
            component: log.component,
            action: log.action,
            endpoint: log.endpoint,
            status: log.error.status,
            message: log.error.message
        })));
        return logs;
    };

    window.clearAppErrorLogs = () => {
        clearErrorLogs();
        console.log('Error logs cleared');
    };

    window.downloadErrorLogs = () => {
        const logs = getErrorLogs();
        const dataStr = JSON.stringify(logs, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `error-logs-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
    };
}
