//express
const express = require("express");

//Controller-bd-action
const ctrl = require("../controllers/reservation_controller");
const Users = express.Router();

/** Users Routes */
Users.route("/reservation").get(ctrl.get);
Users.route("/reservation").post(ctrl.insert);
Users.route("/reservation/:id").get(ctrl.getById);
Users.route("/reservation/:id").put(ctrl.update);
Users.route("/reservation/:id").delete(ctrl.delete);

module.exports = Users;
