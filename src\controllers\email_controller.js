const nodemailer = require("nodemailer");
const { getTransroute } = require("../views/emails");

// Función para obtener la configuración SMTP
const getSmtp = () => {
   var smtpTransport = nodemailer.createTransport({
		host: 'mail.transroute.com.mx',
		port: 465,
		secure: true,
		auth: {
			user: '<EMAIL>',
			pass: 'pX6^284ch*pGL5VFFafX'
		}
	});
	return smtpTransport;
};

// Función para enviar correos
exports.send = (req, res) => {
    let { email_to, data, resend } = req.body;
    // console.log(email_to);
    // console.log(req.body);
    // Validar que se haya proporcionado un destinatario
    if (!req.body.email_to) {
        return res.status(405).jsonp({ send: false, message: "El destinatario del correo no fue proporcionado." });
    }

    let mailOptions = {};

    // Configuración del correo según el entorno
    if (process.env.ENVIRONMENT === "DEVELOPMENT") {
        mailOptions = {
            from: "Transroute RCI - DEV <<EMAIL>>",
            to: resend ? "<EMAIL>" : email_to,
            subject: resend ? "RCI - DevLocal" : "RCI - DevLocal NR",
            html: getTransroute(data),
            ...(resend ? {} : { bcc: '<EMAIL>' })
        };
    } else if (process.env.ENVIRONMENT === "PRODUCTION") {
        mailOptions = {
            from: "Transroute - RCI <<EMAIL>>",
            to: email_to,
            subject: "RCI - Transroute Reservation",
            html: getTransroute(data),
            ...(resend ? { bcc: "<EMAIL>,<EMAIL>" } : {
                bcc: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
            })
        };
    }

    // Enviar el correo
    getSmtp().sendMail(mailOptions, (error, response) => {
        if (error) {
            console.error("Error al enviar el correo:", error);
            return res.status(500).jsonp({ message: `Error al enviar el correo: ${error.message}` });
        }
        res.status(200).jsonp({ message: "El correo fue enviado exitosamente." });
    });
};
