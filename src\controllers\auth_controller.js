const { pool } = require("../database");
const jwt = require("jsonwebtoken");
const _key = require("./../utils");

// Login POST Request
exports.login = function login(req, res) {
    const { usuario, contra } = req.body;

    // Verificar que se proporcionen las credenciales
    if (!usuario || !contra) {
        return res.status(400).json({ status: 400, message: "Usuario y contraseña son requeridos." });
    }

    // Consulta SQL para verificar las credenciales
    const sql = "SELECT * FROM USUARIOS WHERE USUARIO = ? AND CONTRA = md5(?) AND ACTIVO = 1";
    const values = [usuario, contra];

    // Ejecutar la consulta
    pool.query(sql, values, (error, results) => {
        if (error) {
            console.error("Error en la consulta:", error);
            return res.status(500).json({ status: 500, message: "Error en el servidor." });
        }

        // Verificar si se encontraron resultados
        if (results.length > 0) {
            const user = { ...results[0] }; // Obtener el primer usuario
            delete user.CONTRA; // Eliminar la contraseña del objeto de usuario

            // Crear el token JWT
            const _token = jwt.sign(
                { id: user.ID_USUARIO, id_rol: user.NIVEL },
                _key.salat,
                { expiresIn: "24h" } // Duración del token
            );

            // Respuesta exitosa
            const jResponse = {
                status: 200,
                auth: true,
                token: _token,
                results: [user],
            };
            return res.status(200).json(jResponse);
        } else {
            // Respuesta de fallo en el inicio de sesión
            console.log("Login fallido");
            return res.status(401).json({ status: 401, token: null, results: [] });
        }
    });
};