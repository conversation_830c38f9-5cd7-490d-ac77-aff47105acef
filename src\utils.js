module.exports = {
    salat: "innversasalat2020.5!Wr67",
    isEmpty: (obj) => {
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) return false;
        }
        return true;
    },
    functionValidate: (array, callback) => {
        var validationState = true;
        for (var i = 0, len = array.length; i < len; i++) {
            if (array[i] === "" || typeof array[i] === "undefined" || array[i] === null) {
                validationState = false;
                break;
            }
        }
        callback(validationState);
    },
    functionValidateObj: (object, callback) => {
        var validationState = true;

        Object.keys(object).forEach((e) => {
            if (object[e] === "" || object[e] === null) {
                validationState = false;
            }
        });

        let empty = true;
        for (var key in object) {
            if (object.hasOwnProperty(key)) empty = false;
        }
        if (empty) {
            validationState = false;
        }

        callback(validationState);
    },
    userParser: (users) => {
        users.forEach((item) => {
            delete item.CONTRA;
        });
        return users;
    },
};
